from discord.ext import commands
from math import log, floor
import discord, time, datetime, aiohttp, random, os, json
from typing import Union, Optional
from config.constants import Emojis, Colors


class HTTP:
    def __init__(self, headers: Optional[dict] = None, proxy: bool = False) -> None:
        self.headers = headers or {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.169 Safari/537.36"
        }
        self.get = self.json
        if proxy:
            self.proxy = lambda: random.choice(
                os.environ.get("PROXIES", "").split("||")
            )
        else:
            self.proxy = lambda: None

    async def post_json(
        self,
        url: str,
        data: Optional[dict] = None,
        headers: Optional[dict] = None,
        params: Optional[dict] = None,
        proxy: bool = False,
        ssl: Optional[bool] = None,
    ) -> dict:
        """Send a POST request and get the JSON response"""

        async with aiohttp.ClientSession(
            headers=headers or self.headers
        ) as session:
            async with session.post(
                url, data=data, params=params, proxy=self.proxy(), ssl=ssl
            ) as response:
                return await response.json()

    async def json(
        self,
        url: str,
        headers: Optional[dict] = None,
        params: Optional[dict] = None,
        proxy: bool = False,
        ssl: Optional[bool] = None,
    ) -> dict:
        """Send a GET request and get the JSON response"""

        async with aiohttp.ClientSession(
            headers=headers or self.headers
        ) as session:
            async with session.get(
                url, params=params, proxy=self.proxy(), ssl=ssl
            ) as response:
                return await response.json()

    async def text(
        self,
        url: str,
        headers: Optional[dict] = None,
        params: Optional[dict] = None,
        proxy: bool = False,
        ssl: Optional[bool] = None,
    ) -> str:
        """Send a GET request and get the text response"""

        async with aiohttp.ClientSession(
            headers=headers or self.headers
        ) as session:
            async with session.get(
                url, params=params, proxy=self.proxy(), ssl=ssl
            ) as response:
                return await response.text()

    async def read(
        self,
        url: str,
        headers: Optional[dict] = None,
        params: Optional[dict] = None,
        proxy: bool = False,
        ssl: Optional[bool] = None,
    ) -> bytes:
        """Send a GET request and get the response in bytes"""

        async with aiohttp.ClientSession(
            headers=headers or self.headers
        ) as session:
            async with session.get(
                url, params=params, proxy=self.proxy(), ssl=ssl
            ) as response:
                return await response.read()


class Client:
    def __init__(self):
        self.session = HTTP()

    def _process_placeholders(self, text: str, ctx) -> str:
        """Process placeholders in text"""
        if not ctx:
            return text

        # User placeholders
        text = text.replace('{user.name}', ctx.author.display_name)
        text = text.replace('{user.username}', ctx.author.name)
        text = text.replace('{user.id}', str(ctx.author.id))
        text = text.replace('{user.mention}', ctx.author.mention)
        text = text.replace('{user.avatar}', str(ctx.author.display_avatar.url))
        text = text.replace('{user}', ctx.author.mention)

        # Guild placeholders
        if hasattr(ctx, 'guild') and ctx.guild:
            text = text.replace('{guild.name}', ctx.guild.name)
            text = text.replace('{guild.id}', str(ctx.guild.id))
            text = text.replace('{guild.member_count}', str(ctx.guild.member_count))
            text = text.replace('{server}', ctx.guild.name)
            text = text.replace('{server.id}', str(ctx.guild.id))
            text = text.replace('{count}', str(ctx.guild.member_count))
            if ctx.guild.icon:
                text = text.replace('{guild.icon}', str(ctx.guild.icon.url))

        # Channel placeholders
        if hasattr(ctx, 'channel') and ctx.channel:
            text = text.replace('{channel.name}', ctx.channel.name)
            text = text.replace('{channel.id}', str(ctx.channel.id))
            text = text.replace('{channel.mention}', ctx.channel.mention)

        return text

    async def send_error(self, ctx: commands.Context, message: str):
        """Send an error message"""
        # Process placeholders in the message
        processed_message = self._process_placeholders(message, ctx)
        embed = discord.Embed(
            color=Colors.error,
            description=f"{Emojis.error} {ctx.author.mention}: {processed_message}"
        )
        return await ctx.send(embed=embed)

    async def send_warning(self, interaction: discord.Interaction, message: str, ephemeral: bool = False):
        """Send a warning message via interaction"""
        embed = discord.Embed(
            color=Colors.warn,
            description=f"{Emojis.warn} {message}"
        )
        if interaction.response.is_done():
            return await interaction.followup.send(embed=embed, ephemeral=ephemeral)
        else:
            return await interaction.response.send_message(embed=embed, ephemeral=ephemeral)

    async def send_success(self, ctx: commands.Context, message: str):
        """Send a success message"""
        embed = discord.Embed(
            color=Colors.success,
            description=f"{Emojis.success} {ctx.author.mention}: {message}"
        )
        return await ctx.send(embed=embed)

    async def send_loading(self, ctx: commands.Context, message: str):
        """Send a loading message"""
        embed = discord.Embed(
            color=Colors.default,
            description=f"{Emojis.loading} {message}"
        )
        return await ctx.send(embed=embed)

    async def send_default(self, ctx: commands.Context, message: str):
        """Send a default message without emoji"""
        embed = discord.Embed(
            color=Colors.default,
            description=f"{message}"
        )
        return await ctx.send(embed=embed)

    async def send_warn(self, ctx: commands.Context, message: str):
        """Send a warning message"""
        # Process placeholders in the message
        processed_message = self._process_placeholders(message, ctx)
        embed = discord.Embed(
            color=Colors.warn,
            description=f"{Emojis.warn} {ctx.author.mention}: {processed_message}"
        )
        return await ctx.send(embed=embed)


class EmbedHandler:
    """Simple embed handler for easy usage"""

    def _process_placeholders(self, text: str, ctx) -> str:
        """Process placeholders in text"""
        if not ctx:
            return text

        # User placeholders
        text = text.replace('{user.name}', ctx.author.display_name)
        text = text.replace('{user.username}', ctx.author.name)
        text = text.replace('{user.id}', str(ctx.author.id))
        text = text.replace('{user.mention}', ctx.author.mention)
        text = text.replace('{user.avatar}', str(ctx.author.display_avatar.url))
        text = text.replace('{user}', ctx.author.mention)

        # Guild placeholders
        if hasattr(ctx, 'guild') and ctx.guild:
            text = text.replace('{guild.name}', ctx.guild.name)
            text = text.replace('{guild.id}', str(ctx.guild.id))
            text = text.replace('{guild.member_count}', str(ctx.guild.member_count))
            text = text.replace('{server}', ctx.guild.name)
            text = text.replace('{server.id}', str(ctx.guild.id))
            text = text.replace('{count}', str(ctx.guild.member_count))
            if ctx.guild.icon:
                text = text.replace('{guild.icon}', str(ctx.guild.icon.url))

        # Channel placeholders
        if hasattr(ctx, 'channel') and ctx.channel:
            text = text.replace('{channel.name}', ctx.channel.name)
            text = text.replace('{channel.id}', str(ctx.channel.id))
            text = text.replace('{channel.mention}', ctx.channel.mention)

        return text

    async def success(self, ctx: commands.Context, message: str):
        """Send a success message"""
        embed = discord.Embed(
            color=Colors.success,
            description=f"{Emojis.success} {ctx.author.mention}: {message}"
        )
        return await ctx.send(embed=embed)

    async def error(self, ctx: commands.Context, message: str):
        """Send an error message"""
        # Process placeholders in the message
        processed_message = self._process_placeholders(message, ctx)
        embed = discord.Embed(
            color=Colors.error,
            description=f"{Emojis.error} {ctx.author.mention}: {processed_message}"
        )
        return await ctx.send(embed=embed)

    async def warn(self, ctx: commands.Context, message: str):
        """Send a warning message"""
        # Process placeholders in the message
        processed_message = self._process_placeholders(message, ctx)
        embed = discord.Embed(
            color=Colors.warn,
            description=f"{Emojis.warn} {ctx.author.mention}: {processed_message}"
        )
        return await ctx.send(embed=embed)

    async def loading(self, ctx: commands.Context, message: str):
        """Send a loading message"""
        embed = discord.Embed(
            color=Colors.default,
            description=f"{Emojis.loading} {message}"
        )
        return await ctx.send(embed=embed)

    async def default(self, ctx: commands.Context, message: str):
        """Send a default message without emoji"""
        embed = discord.Embed(
            color=Colors.default,
            description=f"{message}"
        )
        return await ctx.send(embed=embed)

    def create_success(self, message: str, ctx=None) -> discord.Embed:
        """Create a success embed without sending"""
        if ctx:
            return discord.Embed(
                color=Colors.success,
                description=f"{Emojis.success} {ctx.author.mention}: {message}"
            )
        return discord.Embed(
            color=Colors.success,
            description=f"{Emojis.success} {message}"
        )

    def create_error(self, message: str, ctx=None) -> discord.Embed:
        """Create an error embed without sending"""
        if ctx:
            return discord.Embed(
                color=Colors.error,
                description=f"{Emojis.error} {ctx.author.mention}: {message}"
            )
        return discord.Embed(
            color=Colors.error,
            description=f"{Emojis.error} {message}"
        )

    def create_loading(self, message: str) -> discord.Embed:
        """Create a loading embed without sending"""
        return discord.Embed(
            color=Colors.default,
            description=f"{Emojis.loading} {message}"
        )

    def create_default(self, message: str) -> discord.Embed:
        """Create a default embed without emoji"""
        return discord.Embed(
            color=Colors.default,
            description=f"{message}"
        )


# Create global embed instance
embed = EmbedHandler()


class Client:
    def ordinal(self, num: int) -> str:
        """Convert number to ordinal (1st, 2nd, 3rd, etc.)"""
        suffix = ['th', 'st', 'nd', 'rd', 'th'][min(num % 10, 4)]
        if 11 <= (num % 100) <= 13:
            suffix = 'th'
        return str(num) + suffix

    def human_format(self, number: int) -> str:
        """Format large numbers in human readable format"""
        if number == 0:
            return "0"
        
        units = ['', 'K', 'M', 'B', 'T']
        k = 1000.0
        magnitude = int(floor(log(number, k)))
        
        if magnitude >= len(units):
            return f"{number:.2e}"
        
        return f"{number / k**magnitude:.1f}{units[magnitude]}"

    def format_timespan(self, seconds: int) -> str:
        """Format seconds into human readable timespan"""
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            return f"{seconds // 60}m {seconds % 60}s"
        elif seconds < 86400:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours}h {minutes}m"
        else:
            days = seconds // 86400
            hours = (seconds % 86400) // 3600
            return f"{days}d {hours}h"

    def get_relative_time(self, timestamp: datetime.datetime) -> str:
        """Get relative time string"""
        now = datetime.datetime.utcnow()
        diff = now - timestamp
        
        if diff.days > 0:
            return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        else:
            return "Just now"

    async def get_user_info(self, user_id: int) -> Optional[dict]:
        """Get user information from Discord API"""
        try:
            data = await self.session.json(
                f"https://discord.com/api/v10/users/{user_id}",
                headers={"Authorization": f"Bot {os.getenv('BOT_TOKEN')}"}
            )
            return data
        except:
            return None

    def create_embed(
        self,
        title: str = None,
        description: str = None,
        color: int = None,
        **kwargs
    ) -> discord.Embed:
        """Create a standardized embed"""
        embed = discord.Embed(
            title=title,
            description=description,
            color=color or Colors.default,
            **kwargs
        )
        return embed

    def create_success_embed(self, message: str, ctx=None) -> discord.Embed:
        """Create a success embed with green color and check emoji"""
        if ctx:
            return discord.Embed(
                color=Colors.success,
                description=f"{Emojis.success} {ctx.author.mention}: {message}"
            )
        return discord.Embed(
            color=Colors.success,
            description=f"{Emojis.success} {message}"
        )

    def create_error_embed(self, message: str, ctx=None) -> discord.Embed:
        """Create an error embed with red color and error emoji"""
        # Process placeholders in the message
        processed_message = self._process_placeholders(message, ctx) if ctx else message
        if ctx:
            return discord.Embed(
                color=Colors.error,
                description=f"{Emojis.error} {ctx.author.mention}: {processed_message}"
            )
        return discord.Embed(
            color=Colors.error,
            description=f"{Emojis.error} {processed_message}"
        )

    def create_warning_embed(self, message: str, ctx=None) -> discord.Embed:
        """Create a warning embed with yellow color and warning emoji"""
        # Process placeholders in the message
        processed_message = self._process_placeholders(message, ctx) if ctx else message
        if ctx:
            return discord.Embed(
                color=Colors.warn,
                description=f"{Emojis.warn} {ctx.author.mention}: {processed_message}"
            )
        return discord.Embed(
            color=Colors.warn,
            description=f"{Emojis.warn} {processed_message}"
        )

    def create_loading_embed(self, message: str) -> discord.Embed:
        """Create a loading embed with animated loading emoji"""
        return discord.Embed(
            color=Colors.default,
            description=f"{Emojis.loading} {message}"
        )

    def create_info_embed(self, message: str) -> discord.Embed:
        """Create an info embed"""
        return discord.Embed(
            color=Colors.default,
            description=f"{Emojis.info} {message}"
        )

    def chunk_list(self, lst: list, chunk_size: int) -> list:
        """Split a list into chunks of specified size"""
        return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]

    def clean_content(self, content: str, max_length: int = 2000) -> str:
        """Clean and truncate content for Discord"""
        if len(content) > max_length:
            return content[:max_length - 3] + "..."
        return content

    async def is_url_valid(self, url: str) -> bool:
        """Check if a URL is valid and accessible"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.head(url) as response:
                    return response.status == 200
        except:
            return False

    def get_permissions_list(self, permissions: discord.Permissions) -> list:
        """Get a list of permission names from a Permissions object"""
        perms = []
        for perm, value in permissions:
            if value:
                perms.append(perm.replace('_', ' ').title())
        return perms

    def format_permissions(self, permissions: discord.Permissions) -> str:
        """Format permissions into a readable string"""
        if permissions.administrator:
            return "Administrator"
        
        perms = self.get_permissions_list(permissions)
        if not perms:
            return "No special permissions"
        
        if len(perms) > 5:
            return f"{', '.join(perms[:5])} and {len(perms) - 5} more"
        
        return ', '.join(perms)
