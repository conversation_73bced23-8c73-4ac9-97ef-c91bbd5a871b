import aiohttp
import os
from discord.ext import commands
from tools.ext import embed
from cogs.old.auth import owners


def is_donor():
    """Check if user is a donor or bot owner"""
    async def predicate(ctx: commands.Context):
        # Check if bot owner first
        if ctx.author.id in owners:
            return True
            
        # Check if donor
        try:
            async with ctx.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM donor WHERE user_id = %s", (ctx.author.id,)
                    )
                    check = await cursor.fetchone()
            
            if not check:
                await embed.error(ctx, "This command is only available for **donors**")
                return False
            return True
        except Exception:
            await embed.error(ctx, "Database error occurred")
            return False
    return commands.check(predicate)


class AskAI(commands.Cog):
    """AI-powered embed assistance for donors"""

    def __init__(self, bot):
        self.bot = bot
        self.api_key = os.getenv('DEEP_API')
        self.api_url = "https://api.deepai.org/api/text-generator"

    async def call_deep_ai(self, prompt: str) -> str:
        """Call Deep AI API with the given prompt"""
        if not self.api_key:
            return "Deep AI API key not configured"
        
        headers = {
            'api-key': self.api_key,
            'Content-Type': 'application/json'
        }
        
        data = {
            'text': prompt
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.api_url, headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get('output', 'No response from AI')
                    else:
                        return f"API Error: {response.status}"
        except Exception as e:
            return f"Error calling AI: {str(e)}"



    @commands.command(name="ask", description="Ask AI to help with embed creation or modification")
    @is_donor()
    async def ask_ai(self, ctx: commands.Context, *, prompt: str):
        """Ask AI to help with embed creation or modification"""
        try:
            # Show typing indicator
            async with ctx.typing():
                # Check if user provided an embed code to modify
                if any(keyword in prompt.lower() for keyword in ['fix', 'modify', 'change', 'update', 'improve']) and '{' in prompt:
                    # Look for embed code in the prompt
                    embed_start = prompt.find('{')
                    embed_end = prompt.rfind('}') + 1
                    embed_code = prompt[embed_start:embed_end]
                    instruction = prompt[:embed_start].strip() + " " + prompt[embed_end:].strip()

                    ai_prompt = f"""You are an expert Discord embed creator. The user wants to {instruction.strip()}.

Current embed code: {embed_code}

Please provide an improved embed code using this exact format:
{{content: message content}}$v{{description: embed description}}$v{{color: #hexcolor}}$v{{thumbnail: image_url}}$v{{button: label|url|style}}

Rules:
- Use $v to separate different parts
- For buttons: label|url|link for link buttons, label||primary for colored buttons
- Colors should be hex codes like #1e1f22
- Support placeholders like {{user.mention}}, {{user.avatar}}, {{server.name}}
- Keep descriptions concise and well-formatted
- Use > for quote formatting in descriptions
- For 3 buttons with middle link: {{button: 🔒||disabled}}$v{{button: voice|https://discord.com/channels/1384275785325608970/1391022408986529843|link}}$v{{button: 🔒||disabled}}

Provide ONLY the embed code, no explanations."""



                else:
                    # General embed creation request
                    ai_prompt = f"""You are an expert Discord embed creator. Create an embed based on this request: {prompt}

Please create an embed code using this exact format:
{{content: message content}}$v{{description: embed description}}$v{{color: #hexcolor}}$v{{thumbnail: image_url}}$v{{button: label|url|style}}

Rules:
- Use $v to separate different parts
- For buttons: label|url|link for link buttons, label||primary for colored buttons, emoji||disabled for disabled buttons
- Colors should be hex codes like #1e1f22
- Support placeholders like {{user.mention}}, {{user.avatar}}, {{server.name}}
- Keep descriptions concise and well-formatted
- Use > for quote formatting in descriptions

Provide ONLY the embed code, no explanations."""

                # Get AI response
                ai_response = await self.call_deep_ai(ai_prompt)
                
                # Clean up the response to extract just the embed code
                embed_code = ai_response.strip()

                # Remove any explanatory text and keep only the embed code
                lines = embed_code.split('\n')
                for line in lines:
                    if '{' in line and ('content:' in line or 'description:' in line or 'title:' in line):
                        embed_code = line.strip()
                        break

                # Send only the code in a code block
                await ctx.send(f"```{embed_code}```")
                    
        except Exception as e:
            await embed.error(ctx, f"Error processing AI request: {str(e)}")


async def setup(bot):
    await bot.add_cog(AskAI(bot))
